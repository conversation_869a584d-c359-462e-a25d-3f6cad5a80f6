import type { SupportedCurrency } from "@/components/dashboard/currency-selector";

const STORAGE_KEY_PREFIX = "portavio-dashboard-preferences";

export interface DashboardPreferences {
  selectedPortfolios: string[];
  displayCurrency: SupportedCurrency;
  timestamp: string;
}

interface DashboardPreferencesData {
  preferences: DashboardPreferences;
  version: string; // For future migration compatibility
}

const CURRENT_VERSION = "1.0.0";

/**
 * Generate user-specific storage key
 */
function getUserStorageKey(userId: string): string {
  if (!userId || typeof userId !== "string") {
    throw new Error("Invalid user ID provided for localStorage key generation");
  }
  // Sanitize user ID to prevent injection attacks
  const sanitizedUserId = userId.replace(/[^a-zA-Z0-9-_]/g, "");
  return `${STORAGE_KEY_PREFIX}-${sanitizedUserId}`;
}

/**
 * Get the current dashboard preferences from localStorage for a specific user
 * This is a utility function for server-side or non-hook usage
 */
export function getDashboardPreferences(
  userId: string
): Partial<DashboardPreferences> | null {
  if (typeof window === "undefined") {
    return null;
  }

  try {
    const storageKey = getUserStorageKey(userId);
    const stored = localStorage.getItem(storageKey);
    if (stored) {
      const data: DashboardPreferencesData = JSON.parse(stored);
      return data.preferences;
    }
  } catch (error) {
    console.error(
      "Error reading dashboard preferences from localStorage:",
      error
    );
  }

  return null;
}

/**
 * Save dashboard preferences to localStorage for a specific user
 */
export function saveDashboardPreferences(
  userId: string,
  preferences: DashboardPreferences
): void {
  if (typeof window === "undefined") {
    return;
  }

  const data: DashboardPreferencesData = {
    preferences: {
      ...preferences,
      timestamp: new Date().toISOString(),
    },
    version: CURRENT_VERSION,
  };

  try {
    const storageKey = getUserStorageKey(userId);
    localStorage.setItem(storageKey, JSON.stringify(data));
  } catch (error) {
    console.error("Error saving dashboard preferences to localStorage:", error);
  }
}

/**
 * Clear dashboard preferences from localStorage for a specific user
 */
export function clearDashboardPreferences(userId: string): void {
  if (typeof window === "undefined") {
    return;
  }

  try {
    const storageKey = getUserStorageKey(userId);
    localStorage.removeItem(storageKey);
  } catch (error) {
    console.error(
      "Error clearing dashboard preferences from localStorage:",
      error
    );
  }
}

/**
 * Clear all user-specific data from localStorage (for logout)
 */
export function clearAllUserData(userId: string): void {
  if (typeof window === "undefined") {
    return;
  }

  try {
    // Clear dashboard preferences
    clearDashboardPreferences(userId);

    // Note: Cookie consent and theme preferences are intentionally not cleared
    // as they are user-preference settings that should persist across sessions
  } catch (error) {
    console.error("Error clearing user data from localStorage:", error);
  }
}

/**
 * Get default dashboard preferences
 */
export function getDefaultDashboardPreferences(): DashboardPreferences {
  return {
    selectedPortfolios: [],
    displayCurrency: "EUR",
    timestamp: new Date().toISOString(),
  };
}
